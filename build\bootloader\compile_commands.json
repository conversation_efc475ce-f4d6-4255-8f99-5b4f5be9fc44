[{"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -o CMakeFiles\\bootloader.elf.dir\\project_elf_src_esp32.c.obj -c C:\\Users\\<USER>\\project-ESP32S3\\build\\bootloader\\project_elf_src_esp32.c", "file": "C:\\Users\\<USER>\\project-ESP32S3\\build\\bootloader\\project_elf_src_esp32.c", "output": "CMakeFiles\\bootloader.elf.dir\\project_elf_src_esp32.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\xtensa\\CMakeFiles\\__idf_xtensa.dir\\eri.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\xtensa\\eri.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\xtensa\\eri.c", "output": "esp-idf\\xtensa\\CMakeFiles\\__idf_xtensa.dir\\eri.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\xtensa\\CMakeFiles\\__idf_xtensa.dir\\xt_trax.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\xtensa\\xt_trax.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\xtensa\\xt_trax.c", "output": "esp-idf\\xtensa\\CMakeFiles\\__idf_xtensa.dir\\xt_trax.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\lldesc.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\lldesc.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\lldesc.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\lldesc.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\dport_access_common.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\dport_access_common.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\dport_access_common.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\dport_access_common.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\interrupts.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\interrupts.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\interrupts.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\interrupts.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\gpio_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\gpio_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\gpio_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\gpio_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\uart_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\uart_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\uart_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\uart_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\dport_access.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\dport_access.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\dport_access.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\dport_access.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\adc_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\adc_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\adc_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\adc_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\emac_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\emac_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\emac_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\emac_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\spi_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\spi_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\spi_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\spi_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\ledc_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\ledc_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\ledc_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\ledc_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\pcnt_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\pcnt_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\pcnt_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\pcnt_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\rmt_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\rmt_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\rmt_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\rmt_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\sdm_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\sdm_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\sdm_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\sdm_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\i2s_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\i2s_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\i2s_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\i2s_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\i2c_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\i2c_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\i2c_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\i2c_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\timer_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\timer_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\timer_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\timer_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\lcd_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\lcd_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\lcd_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\lcd_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\mcpwm_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\mcpwm_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\mcpwm_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\mcpwm_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\mpi_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\mpi_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\mpi_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\mpi_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\sdmmc_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\sdmmc_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\sdmmc_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\sdmmc_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\touch_sensor_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\touch_sensor_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\touch_sensor_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\touch_sensor_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\twai_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\twai_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\twai_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\twai_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\wdt_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\wdt_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\wdt_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\wdt_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\dac_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\dac_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\dac_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\dac_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\rtc_io_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\rtc_io_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\rtc_io_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\rtc_io_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\sdio_slave_periph.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\sdio_slave_periph.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\soc\\esp32\\sdio_slave_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32\\sdio_slave_periph.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\micro-ecc\\CMakeFiles\\__idf_micro-ecc.dir\\uECC_verify_antifault.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader\\subproject\\components\\micro-ecc\\uECC_verify_antifault.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader\\subproject\\components\\micro-ecc\\uECC_verify_antifault.c", "output": "esp-idf\\micro-ecc\\CMakeFiles\\__idf_micro-ecc.dir\\uECC_verify_antifault.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\hal_utils.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\hal_utils.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\hal_utils.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\hal_utils.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mpu_hal.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\mpu_hal.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\mpu_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mpu_hal.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\efuse_hal.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\efuse_hal.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\efuse_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\efuse_hal.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\esp32\\efuse_hal.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\esp32\\efuse_hal.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\esp32\\efuse_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\esp32\\efuse_hal.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\wdt_hal_iram.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\wdt_hal_iram.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\wdt_hal_iram.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\wdt_hal_iram.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mmu_hal.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\mmu_hal.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\mmu_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mmu_hal.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\esp32\\cache_hal_esp32.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\esp32\\cache_hal_esp32.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\hal\\esp32\\cache_hal_esp32.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\esp32\\cache_hal_esp32.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include/spi_flash -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\spi_flash\\CMakeFiles\\__idf_spi_flash.dir\\spi_flash_wrap.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\spi_flash\\spi_flash_wrap.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\spi_flash\\spi_flash_wrap.c", "output": "esp-idf\\spi_flash\\CMakeFiles\\__idf_spi_flash.dir\\spi_flash_wrap.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_bootloader_format\\CMakeFiles\\__idf_esp_bootloader_format.dir\\esp_bootloader_desc.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_bootloader_format\\esp_bootloader_desc.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_bootloader_format\\esp_bootloader_desc.c", "output": "esp-idf\\esp_bootloader_format\\CMakeFiles\\__idf_esp_bootloader_format.dir\\esp_bootloader_desc.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_common.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_common.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common_loader.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_common_loader.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_common_loader.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common_loader.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_init.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_clock_init.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_clock_init.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_init.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_mem.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_mem.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_mem.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_mem.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_random.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_random.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_efuse.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_efuse.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_efuse.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_efuse.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_encrypt.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\flash_encrypt.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\flash_encrypt.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_encrypt.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\secure_boot.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\secure_boot.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\secure_boot.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\secure_boot.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random_esp32.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_random_esp32.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_random_esp32.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random_esp32.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\flash_qio_mode.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\bootloader_flash\\src\\flash_qio_mode.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\bootloader_flash\\src\\flash_qio_mode.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\flash_qio_mode.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash_config_esp32.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash_config_esp32.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash_config_esp32.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash_config_esp32.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_utility.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_utility.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_utility.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_utility.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_partitions.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\flash_partitions.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\flash_partitions.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_partitions.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp_image_format.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\esp_image_format.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\esp_image_format.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp_image_format.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_init.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_init.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_init.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_init.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_loader.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_clock_loader.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_clock_loader.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_loader.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_console.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_console.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console_loader.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_console_loader.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_console_loader.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console_loader.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32\\bootloader_sha.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\esp32\\bootloader_sha.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\esp32\\bootloader_sha.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32\\bootloader_sha.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32\\bootloader_soc.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\esp32\\bootloader_soc.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\esp32\\bootloader_soc.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32\\bootloader_soc.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32\\bootloader_esp32.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\esp32\\bootloader_esp32.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\esp32\\bootloader_esp32.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32\\bootloader_esp32.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DN<PERSON>_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_panic.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_panic.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader_support\\src\\bootloader_panic.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_panic.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32\\esp_efuse_table.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\esp32\\esp_efuse_table.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\esp32\\esp_efuse_table.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32\\esp_efuse_table.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32\\esp_efuse_fields.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\esp32\\esp_efuse_fields.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\esp32\\esp_efuse_fields.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32\\esp_efuse_fields.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32\\esp_efuse_utility.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\esp32\\esp_efuse_utility.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\esp32\\esp_efuse_utility.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32\\esp_efuse_utility.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_api.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\src\\esp_efuse_api.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\src\\esp_efuse_api.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_api.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_fields.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\src\\esp_efuse_fields.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\src\\esp_efuse_fields.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_fields.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_utility.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\src\\esp_efuse_utility.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\src\\esp_efuse_utility.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_utility.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\efuse_controller\\keys\\without_key_purposes\\three_key_blocks\\esp_efuse_api_key.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\src\\efuse_controller\\keys\\without_key_purposes\\three_key_blocks\\esp_efuse_api_key.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\efuse\\src\\efuse_controller\\keys\\without_key_purposes\\three_key_blocks\\esp_efuse_api_key.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\efuse_controller\\keys\\without_key_purposes\\three_key_blocks\\esp_efuse_api_key.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_system\\CMakeFiles\\__idf_esp_system.dir\\esp_err.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_system\\esp_err.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_system\\esp_err.c", "output": "esp-idf\\esp_system\\CMakeFiles\\__idf_esp_system.dir\\esp_err.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\cpu.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\cpu.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\cpu.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\cpu.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\esp_cpu_intr.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\esp_cpu_intr.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\esp_cpu_intr.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\esp_cpu_intr.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\esp_memory_utils.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\esp_memory_utils.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\esp_memory_utils.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\esp_memory_utils.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\cpu_region_protect.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\cpu_region_protect.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\cpu_region_protect.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\cpu_region_protect.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\rtc_clk.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\rtc_clk.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\rtc_clk.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\rtc_clk.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\rtc_clk_init.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\rtc_clk_init.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\rtc_clk_init.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\rtc_clk_init.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\rtc_init.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\rtc_init.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\rtc_init.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\rtc_init.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\rtc_sleep.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\rtc_sleep.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\rtc_sleep.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\rtc_sleep.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\rtc_time.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\rtc_time.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\rtc_time.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\rtc_time.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\chip_info.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\chip_info.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_hw_support\\port\\esp32\\chip_info.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32\\chip_info.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_common\\CMakeFiles\\__idf_esp_common.dir\\src\\esp_err_to_name.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_common\\src\\esp_err_to_name.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_common\\src\\esp_err_to_name.c", "output": "esp-idf\\esp_common\\CMakeFiles\\__idf_esp_common.dir\\src\\esp_err_to_name.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_sys.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_sys.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_sys.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_sys.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_print.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_print.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_print.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_print.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_crc.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_crc.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_crc.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_crc.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_uart.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_uart.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_uart.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_uart.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_spiflash.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_spiflash.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_spiflash.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_spiflash.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_efuse.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_efuse.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_efuse.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_efuse.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_gpio.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_gpio.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_gpio.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_gpio.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/include -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_longjmp.S.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_longjmp.S", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\esp_rom\\patches\\esp_rom_longjmp.S", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_longjmp.S.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\noos\\log_timestamp.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\log\\src\\noos\\log_timestamp.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\log\\src\\noos\\log_timestamp.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\noos\\log_timestamp.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\log_timestamp_common.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\log\\src\\log_timestamp_common.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\log\\src\\log_timestamp_common.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\log_timestamp_common.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include/esp_private -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\noos\\log_lock.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\log\\src\\noos\\log_lock.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\log\\src\\noos\\log_lock.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\noos\\log_lock.c.obj"}, {"directory": "C:/Users/<USER>/project-ESP32S3/build/bootloader", "command": "C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.4.2\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -IC:/Users/<USER>/project-ESP32S3/build/bootloader/config -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32/include/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/include/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/debug_probe/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/. -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/port/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/xtensa/deprecated_include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32 -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/esp32/register -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader_support/private_include -mlongcalls -Wno-frame-address  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/subproject=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.4.2=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\main\\CMakeFiles\\__idf_main.dir\\bootloader_start.c.obj -c C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader\\subproject\\main\\bootloader_start.c", "file": "C:\\Espressif\\frameworks\\esp-idf-v5.4.2\\components\\bootloader\\subproject\\main\\bootloader_start.c", "output": "esp-idf\\main\\CMakeFiles\\__idf_main.dir\\bootloader_start.c.obj"}]