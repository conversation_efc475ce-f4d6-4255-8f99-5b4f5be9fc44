{"C_Cpp.intelliSenseEngine": "default", "idf.espIdfPathWin": "C:/Espressif/frameworks/esp-idf-v5.4.2/", "idf.pythonInstallPath": "C:\\Espressif\\tools\\idf-python\\3.11.2\\python.exe", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.portWin": "COM26", "idf.toolsPathWin": "C:\\Espressif", "idf.customExtraVars": {"IDF_TARGET": "esp32s3"}, "clangd.path": "C:\\Espressif\\tools\\esp-clang\\esp-18.1.2_20240912\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe", "--compile-commands-dir=c:\\Users\\<USER>\\project-ESP32S3\\build"], "idf.flashType": "UART", "files.associations": {"task.h": "c"}}