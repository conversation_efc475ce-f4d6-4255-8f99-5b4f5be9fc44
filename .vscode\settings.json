{"C_Cpp.intelliSenseEngine": "default", "idf.espIdfPathWin": "C:/Espressif/frameworks/esp-idf-v5.4.2/", "idf.pythonInstallPath": "C:\\Espressif\\tools\\idf-python\\3.11.2\\python.exe", "idf.openOcdConfigs": ["board/esp32-wrover-kit-3.3v.cfg"], "idf.portWin": "COM26", "idf.toolsPathWin": "C:\\Espressif", "idf.customExtraVars": {"IDF_TARGET": "esp32"}, "clangd.path": "C:\\Espressif\\tools\\esp-clang\\esp-18.1.2_20240912\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=C:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe", "--compile-commands-dir=${workspaceFolder}\\build"], "idf.flashType": "UART"}