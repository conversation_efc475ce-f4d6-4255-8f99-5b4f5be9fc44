# ninja log v6
54	292	7775556998051315	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	2f53408606e4058d
35	321	7775556997868159	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	e22696d66731aaae
21	374	7775556997729843	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	7709fff979ed4a45
29	382	7775556997810134	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	de27e00a75af4a1d
73	396	7775556998240811	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	2e7aa4d40810e338
41	426	7775556997931883	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	a359738182421a0e
86	443	7775556998375935	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	c8003a9f737d20cb
107	460	7775556998584504	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/esp_cpu_intr.c.obj	9675111c6cb8f5d1
79	481	7775556998308492	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	4be08068c7db7ba0
61	489	7775556998129000	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	d0c199bd2818227
114	497	7775556998657463	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	bbd401f6a0283049
94	504	7775556998448743	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	6404cd15d51fc05b
121	521	7775556998727300	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/cpu_region_protect.c.obj	67ae349e0ffa6243
47	532	7775556997988408	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	2ac4b27d7a2941f7
100	564	7775556998526707	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	cbe1a9b3831148f7
141	645	7775556998920818	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk_init.c.obj	50fc02578f389ec7
427	707	7775557001785859	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	354dbcd310d98747
383	714	7775557001335356	esp-idf/log/liblog.a	17a3f9356f151ad0
67	739	7775556998178981	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	a4cd1875ddec03cb
293	783	7775557000454162	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_init.c.obj	aa10cff9c4a6e981
443	809	7775557001946220	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_table.c.obj	4eb8723431f991b1
396	818	7775557001482941	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/chip_info.c.obj	eb15afb8bc8b492
460	879	7775557002118314	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_fields.c.obj	460b0cc965559d6d
497	886	7775557002481876	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	816669c1aac08c2b
322	903	7775557000732322	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_sleep.c.obj	1fba5f9118e93608
489	918	7775557002408808	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	920d50a6ed4f1128
374	942	7775557001265169	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_time.c.obj	6b8a38e42e8696ae
481	951	7775557002327698	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32/esp_efuse_utility.c.obj	363b3d261b5ab60d
129	962	7775556998845515	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32/rtc_clk.c.obj	6ef0411fde312dcf
532	1020	7775557002836015	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	a7e77c519c063173
521	1031	7775557002727314	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c.obj	e29191d9c6a8b417
645	1039	7775557003968713	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	27e6fff786733239
564	1046	7775557003155878	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	c9ab863487e15876
714	1053	7775557004653969	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	1c0736e2382189f6
504	1060	7775557002557184	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	649db1ee5c2a57d6
783	1104	7775557005348925	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	a49e281a88260259
707	1111	7775557004587139	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	f1b2286f407978b8
819	1148	7775557005684079	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	798ef471e99d526d
739	1177	7775557004899333	esp-idf/esp_rom/libesp_rom.a	ebf3ffa62c93f759
879	1225	7775557006297779	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32.c.obj	2fa0b12150a24119
1061	1259	7775557008124560	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_soc.c.obj	7b296d582b5899f1
809	1284	7775557005616762	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	947740ffdbb2431c
952	1334	7775557007043498	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	7f603d9aeb27b768
903	1341	7775557006548545	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	e123288ecb737d33
1031	1348	7775557007829516	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	9934c7e47e527417
1039	1414	7775557007904193	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	4c25e4b71ccfff9e
1046	1456	7775557007973118	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	961941bd0d0613a9
1054	1463	7775557008049142	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_sha.c.obj	738ed5b1c89eedfa
1112	1498	7775557008627554	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	9b2d913de38cf3ff
1148	1506	7775557008996523	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	41648447f9ecb3c1
918	1515	7775557006697265	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32.c.obj	45b6d56c862b26eb
1020	1523	7775557007718146	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	734759d31585156f
1177	1565	7775557009276673	esp-idf/esp_common/libesp_common.a	e46602b512d70808
1259	1621	7775557010111147	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	a8c9fac1f89b30ce
1225	1629	7775557009765107	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	ac5cd3b684d72104
1285	1637	7775557010353421	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	2310fa33f845af2b
886	1689	7775557006377855	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	55727816a0255e5c
1334	1706	7775557010854192	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	60ed748d87793de8
1104	1714	7775557008557271	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32/bootloader_esp32.c.obj	8042d4d076d87a68
1341	1721	7775557010924510	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/efuse_hal.c.obj	61c0c55705309baa
1499	1740	7775557012505592	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	5729b314d0a5f974
942	1747	7775557006941954	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	c7df1a369ee04e05
1515	1757	7775557012675423	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/interrupts.c.obj	94121fa421166740
1507	1818	7775557012587717	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	3a83258b3a147e8f
962	1825	7775557007138603	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	6e4d08935819f2a0
1348	1840	7775557011003494	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	a9e16ba6499c8b3f
1523	1847	7775557012742810	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/gpio_periph.c.obj	9c125be30c68e3f2
1456	1854	7775557012071690	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32/cache_hal_esp32.c.obj	44ee78b79e52559e
1630	1860	7775557013815567	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dport_access.c.obj	ad18f1ad15cb56b8
1637	1868	7775557013883493	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/adc_periph.c.obj	c81d7f4f48aed6cf
1622	1884	7775557013734225	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/uart_periph.c.obj	6c0b75da2c56ae0e
1747	1915	7775557014990963	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdm_periph.c.obj	d708d80b50cdefe
1414	1927	7775557011654316	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	d85a3750a4a84f66
1707	1939	7775557014588106	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/spi_periph.c.obj	e88ac7829bea74c1
1565	1954	7775557013163248	esp-idf/esp_hw_support/libesp_hw_support.a	f6d6be2ad864bbe
1714	1967	7775557014661146	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/ledc_periph.c.obj	415b47e2e94e7de6
1722	1975	7775557014736144	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/pcnt_periph.c.obj	442e68fad3bf6b9c
1740	1982	7775557014920247	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rmt_periph.c.obj	d55351d8c8c17c31
1689	1989	7775557014412942	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/emac_periph.c.obj	2f1434057fd10c95
1757	2014	7775557015091071	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2s_periph.c.obj	e5ec4db5eba21027
1868	2047	7775557016194961	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/touch_sensor_periph.c.obj	a6fd040fa6b52c69
1854	2048	7775557016059206	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mpi_periph.c.obj	d85ad5311a94b0cd
1818	2061	7775557015692819	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/i2c_periph.c.obj	8c0a3edb17d48cc2
1825	2068	7775557015759807	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/timer_periph.c.obj	be8529ed9ccee79a
1840	2070	7775557015911623	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/lcd_periph.c.obj	c3d1406aa77b1225
1847	2082	7775557015988790	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/mcpwm_periph.c.obj	31df2ddb5210a2b3
1989	2084	7775557018235284	project_elf_src_esp32.c	202b2c5366906315
1989	2084	7775557018235284	C:/Users/<USER>/project-ESP32S3/build/bootloader/project_elf_src_esp32.c	202b2c5366906315
1885	2092	7775557016362291	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/twai_periph.c.obj	8fa95e156bfc3def
1861	2094	7775557016126009	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdmmc_periph.c.obj	1b6b2956bafcc485
1915	2113	7775557016650283	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/wdt_periph.c.obj	af65ca83b26565b0
1928	2130	7775557016791906	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/dac_periph.c.obj	3adc58a5d060ace0
1940	2134	7775557016913720	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/rtc_io_periph.c.obj	196de1d59a5b65ea
1975	2136	7775557017268878	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	d99c48a5c89d1627
1967	2164	7775557017193412	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32/sdio_slave_periph.c.obj	4414b2c19ad50a97
1955	2184	7775557017064726	esp-idf/esp_system/libesp_system.a	7201c524d8432737
2085	2197	7775557018365443	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32.c.obj	6c9502215c898600
1982	2216	7775557017334473	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	131edde4dd7b6842
2014	2230	7775557017659306	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	643ed5089e101dd1
2184	2349	7775557019353790	esp-idf/efuse/libefuse.a	23b190ff0954cee3
2350	2549	7775557021012343	esp-idf/bootloader_support/libbootloader_support.a	710445a8cb091eee
2550	2688	7775557023016042	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	be45ac2439c8fc1a
1463	2773	7775557012141036	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	ac8f3e3908f563cc
2689	2839	7775557024405952	esp-idf/spi_flash/libspi_flash.a	4ec97143f44ca377
2839	3006	7775557025904895	esp-idf/hal/libhal.a	401ab0708d7dfef
3006	3133	7775557027559011	esp-idf/micro-ecc/libmicro-ecc.a	9e605b1c2a84b1ff
3134	3343	7775557028846945	esp-idf/soc/libsoc.a	689d9bbff40a965f
3343	3467	7775557030936317	esp-idf/xtensa/libxtensa.a	50c266fc8ec7c41e
3467	3590	7775557032185374	esp-idf/main/libmain.a	7682f23cd0049e60
3590	3896	7775557033416428	bootloader.elf	a8f79d09a4b75113
3896	4250	7775557039933431	.bin_timestamp	339af6940517c5a
3896	4250	7775557039933431	C:/Users/<USER>/project-ESP32S3/build/bootloader/.bin_timestamp	339af6940517c5a
4250	4383	7775557040017519	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	2f3b8c692b279adb
4250	4383	7775557040017519	C:/Users/<USER>/project-ESP32S3/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	2f3b8c692b279adb
29	170	7775557913848458	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	2f3b8c692b279adb
29	170	7775557913848458	C:/Users/<USER>/project-ESP32S3/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	2f3b8c692b279adb
