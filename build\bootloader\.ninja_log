# ninja log v6
53	236	7775560292644827	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	66bd35701cb81206
28	245	7775560292385710	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	488e348576dc686a
20	253	7775560292311725	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	15072c35f7378755
86	267	7775560292967575	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	dde9184bc3664161
73	280	7775560292841521	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	18aa0ea2d2335818
40	288	7775560292509034	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	2d759af6f8757151
33	295	7775560292440799	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	230afc8a9ecac99d
112	308	7775560293232815	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	d028d0f5a67d6f7e
92	323	7775560293019600	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	55131ac60e943955
61	331	7775560292713016	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	66a35d2752abf66c
80	338	7775560292901894	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	d611e24bb624512d
67	345	7775560292777698	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	23a4ccc55c9f5f60
99	360	7775560293104472	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	2f78537cd5d912c0
105	470	7775560293162721	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	d3be61e1ddab580b
134	514	7775560293445178	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	808ad9d6db8a6114
120	546	7775560293304621	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	f37429d959a33ece
46	554	7775560292567065	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	eb224dc4328036a3
246	612	7775560294567962	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	cbbfb8d2a7ea0548
127	623	7775560293385034	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	9a5607066bab890
237	682	7775560294475202	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	1a6af39687c6b2dd
346	794	7775560295561319	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	bd7416c6166ad7af
331	820	7775560295417422	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	44611f70dbf26427
323	841	7775560295338261	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	54d4d11c2de59226
338	868	7775560295485937	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	10d380577eae43ee
295	875	7775560295062645	esp-idf/log/liblog.a	e472f7f67880c08
268	934	7775560294785261	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	3f7adaa52554cdce
361	950	7775560295708320	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	c4b88eb5cb91290e
288	970	7775560294992063	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	d8fdcd1e6c668751
470	1022	7775560296812345	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	472847acace061af
546	1068	7775560297571752	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	465e5c11823e59a
309	1079	7775560295192662	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	8bc2fcbceb9cb451
254	1086	7775560294643654	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	b3cae9b037570323
281	1094	7775560294910655	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	5c1ef0f34d8cfd01
794	1129	7775560300045323	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	f4047c8910f8003a
515	1146	7775560297253074	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	99eca10378bc0787
555	1163	7775560297646920	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	e86bd583a8eb65e5
841	1195	7775560300525488	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	a8fd03b254855c3d
624	1203	7775560298341875	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	36a4970b757b35cc
820	1214	7775560300299482	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	53c6f36c482cb5b1
682	1233	7775560298927453	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	fe54fc546c24a686
876	1287	7775560300863538	esp-idf/esp_rom/libesp_rom.a	50c200dd41346b25
869	1300	7775560300792659	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	1f6fc09c76b320de
612	1308	7775560298223783	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	c0a61c3580beba32
951	1347	7775560301607304	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	bad159eec871dc5b
970	1387	7775560301806149	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	985c1d98215f902c
934	1400	7775560301445343	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	b0812468b320e043
1094	1415	7775560303049079	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	ea8e5a344bbdaf22
1163	1517	7775560303732172	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	62ce964a68e5570b
1068	1528	7775560302784550	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	9a876914dfa315f3
1204	1536	7775560304144620	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	6f9d65ba47c3db3b
1233	1544	7775560304438959	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	e82f1d4110d435d6
1214	1554	7775560304253449	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	1b31e5395bdd4fb5
1079	1581	7775560302895221	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	f60a24d886249a9
1195	1608	7775560304060368	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	536466cd4d2c7c18
1287	1647	7775560304972188	esp-idf/esp_common/libesp_common.a	15a43b2f85edda03
1347	1661	7775560305582972	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	d541b39f580d2b34
1308	1670	7775560305187328	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	815c724bb8b9ab01
1147	1681	7775560303568630	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	f8e553ac09cb57a9
1415	1813	7775560306257199	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	51d36ee12dae4360
1400	1820	7775560306112367	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	1d28ebfadbc0560f
1023	1848	7775560302336053	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	b23e6fa13d4fabcd
1581	1855	7775560307920948	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	5a3afc26c8dbc805
1387	1872	7775560305971639	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	504f3092c75e4d72
1518	1880	7775560307285102	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	b1f0e3e6961bec12
1608	1890	7775560308185167	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	9828e325e18f6d8b
1528	1919	7775560307391801	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	ba8434b13e42b2a0
1670	1926	7775560308805125	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	4c6b8194cfa266e3
1300	1933	7775560305105495	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	d63e4de0d1bd781f
1661	1944	7775560308714036	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	aabdf4dec0d53b90
1681	1982	7775560308922924	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	dc758f640b11e1cb
1536	2001	7775560307467093	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	205322b32e59fa63
1544	2018	7775560307547536	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	39c42a8c045a4fef
1813	2025	7775560310232376	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	9f602bdb8a050e59
1087	2041	7775560302969583	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	a117b4436e4964f6
1647	2060	7775560308571750	esp-idf/esp_hw_support/libesp_hw_support.a	24e103022cbfc8d
1820	2073	7775560310312783	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	340d88f935e22928
1848	2086	7775560310588457	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	8b949cebea0a23cf
1129	2094	7775560303399898	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	9694392e0e8b1cf7
1919	2111	7775560311288498	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	c14917be62ddad69
1891	2119	7775560311010498	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	1890ef51f312f529
1872	2126	7775560310826152	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	14d9ebf99611aeee
1855	2145	7775560310653889	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	1fee4aef94032d8e
1880	2172	7775560310904243	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	d9f3d47f3398d217
2001	2203	7775560312116858	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	9bc7fc0b9cd75789
1926	2218	7775560311357356	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	a17cfc144979e229
1944	2225	7775560311547539	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	2518c9f31ad9b586
1933	2226	7775560311434849	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	2718abd37648dd3
1982	2243	7775560311920939	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	15e5f3d0c68e2dd1
2073	2245	7775560312843116	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	fc89accc49ee4b6d
2172	2257	7775560314541554	project_elf_src_esp32s3.c	2f263cfdde646ccb
2172	2257	7775560314541554	C:/Users/<USER>/project-ESP32S3/build/bootloader/project_elf_src_esp32s3.c	2f263cfdde646ccb
2019	2265	7775560312296650	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	6a4514daca2a10ca
2026	2274	7775560312361758	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	7c47478bc384971d
2111	2280	7775560313224528	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	186b74b82bbe567
2042	2286	7775560312525911	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	19009d44c400b365
2086	2288	7775560312971760	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	86939a862567a28f
2060	2290	7775560312707698	esp-idf/esp_system/libesp_system.a	b987a76d7e3fc015
2094	2303	7775560313045317	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	f6786fd984970d52
2126	2304	7775560313369979	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	59826c12a88befe1
2119	2322	7775560313299543	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	7438fdc6229f931
2258	2359	7775560314686008	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	64096cc6d5eea116
2145	2365	7775560313549376	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	f29fad550e63f82e
2203	2408	7775560314136147	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	d974eeac73890b8a
2290	2469	7775560315009170	esp-idf/efuse/libefuse.a	6f9a49221a6adf4c
2470	2674	7775560316800748	esp-idf/bootloader_support/libbootloader_support.a	39dd7d40c660480d
1555	2765	7775560307651993	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	a262b53756a479cd
2674	2798	7775560318850839	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	7eb6fdf46ea71c6b
2798	2921	7775560320077514	esp-idf/spi_flash/libspi_flash.a	7b2e730b869c6cd3
2921	3068	7775560321316871	esp-idf/hal/libhal.a	544c9bc526926c94
3068	3200	7775560322787425	esp-idf/micro-ecc/libmicro-ecc.a	41c4ce62c2d3285c
3200	3422	7775560324107982	esp-idf/soc/libsoc.a	fbfcc845da6d7947
3422	3561	7775560326322078	esp-idf/xtensa/libxtensa.a	71f2f4adf4c0203e
3561	3697	7775560327714123	esp-idf/main/libmain.a	149a07591d387e01
3697	4020	7775560329075290	bootloader.elf	902661f9217a4a22
4020	4427	7775560336279204	.bin_timestamp	c4f0d459a151b729
4020	4427	7775560336279204	C:/Users/<USER>/project-ESP32S3/build/bootloader/.bin_timestamp	c4f0d459a151b729
4427	4578	7775560336377736	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	72bc95fbbe134025
4427	4578	7775560336377736	C:/Users/<USER>/project-ESP32S3/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	72bc95fbbe134025
